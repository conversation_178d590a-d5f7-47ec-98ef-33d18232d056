"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Loader2, CheckCircle, XCircle, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { configService } from "@/lib/config-service";
import { ConfigFormData, ConfigTestResult } from "@/types/trading";

const configSchema = z.object({
  apiKey: z.string().min(1, "API Key 不能为空"),
  baseURL: z.string().url("请输入有效的 URL"),
  model: z.string().min(1, "请选择模型"),
});

export function ConfigForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<ConfigTestResult | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const [showCustomBaseURL, setShowCustomBaseURL] = useState(false);
  const [showCustomModel, setShowCustomModel] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ConfigFormData>({
    resolver: zodResolver(configSchema),
    defaultValues: {
      apiKey: "",
      baseURL: "https://api.openai.com/v1",
      model: "gpt-4o-mini",
    },
  });

  const watchedValues = watch();

  // 调试日志
  useEffect(() => {
    console.log("watchedValues updated:", watchedValues);
  }, [watchedValues]);

  // 加载现有配置
  useEffect(() => {
    const config = configService.getConfig();
    console.log("Loading config:", config);
    setValue("apiKey", config.apiKey);
    setValue("baseURL", config.baseURL);
    setValue("model", config.model);

    // 设置自定义状态 - 使用setTimeout确保在下一个事件循环中执行
    setTimeout(() => {
      const commonBaseURLs = configService.getCommonBaseURLs();
      const availableModels = configService.getAvailableModels();
      const isCustomBaseURL = !commonBaseURLs.includes(config.baseURL);
      const isCustomModel = !availableModels.includes(config.model);

      console.log("commonBaseURLs:", commonBaseURLs);
      console.log("config.baseURL:", config.baseURL);
      console.log("isCustomBaseURL:", isCustomBaseURL);
      console.log("Setting showCustomBaseURL to:", isCustomBaseURL);

      setShowCustomBaseURL(isCustomBaseURL);
      setShowCustomModel(isCustomModel);
    }, 0);
  }, [setValue]);

  // 保存配置
  const onSubmit = async (data: ConfigFormData) => {
    setIsLoading(true);

    // 显示保存中的提示
    const loadingToast = toast.loading("正在保存配置...");

    try {
      configService.saveConfig(data);
      toast.dismiss(loadingToast);
      toast.success("✅ 配置保存成功！", {
        description: "您的 OpenAI API 配置已成功保存到本地存储",
        duration: 3000,
      });
      setTestResult(null);
    } catch (error: any) {
      toast.dismiss(loadingToast);
      toast.error("❌ 保存失败", {
        description: error.message,
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 测试配置
  const handleTest = async () => {
    setIsTesting(true);
    setTestResult(null);

    // 显示测试中的提示
    const loadingToast = toast.loading("正在测试 API 连接...", {
      description: "请稍候，正在验证您的配置",
    });

    try {
      const result = await configService.testConfig(watchedValues);
      setTestResult(result);

      toast.dismiss(loadingToast);

      if (result.success) {
        toast.success("🎉 配置测试成功！", {
          description: `API 连接正常${
            result.latency ? `，响应时间: ${result.latency}ms` : ""
          }`,
          duration: 4000,
        });
      } else {
        toast.error("❌ 配置测试失败", {
          description: result.message,
          duration: 5000,
        });
      }
    } catch (error: any) {
      const errorMessage = "测试失败: " + error.message;
      setTestResult({
        success: false,
        message: errorMessage,
      });

      toast.dismiss(loadingToast);
      toast.error("❌ 测试失败", {
        description: error.message,
        duration: 5000,
      });
    } finally {
      setIsTesting(false);
    }
  };

  // 重置配置
  const handleReset = () => {
    const confirmToast = toast.loading("正在重置配置...");

    try {
      configService.clearConfig();
      setValue("apiKey", "");
      setValue("baseURL", "https://api.openai.com/v1");
      setValue("model", "gpt-4o-mini");
      setTestResult(null);

      // 重置自定义状态
      setShowCustomBaseURL(false);
      setShowCustomModel(false);

      toast.dismiss(confirmToast);
      toast.success("🔄 配置已重置", {
        description: "所有配置已恢复为默认值，本地存储已清空",
        duration: 3000,
      });
    } catch (error: any) {
      toast.dismiss(confirmToast);
      toast.error("❌ 重置失败", {
        description: error.message,
        duration: 4000,
      });
    }
  };

  const availableModels = configService.getAvailableModels();
  const commonBaseURLs = configService.getCommonBaseURLs();

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>OpenAI API 配置</CardTitle>
        <CardDescription>
          配置您的 OpenAI API 信息以启用 AI 分析功能
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* API Key */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                placeholder="sk-..."
                {...register("apiKey")}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.apiKey && (
              <p className="text-sm text-red-500">{errors.apiKey.message}</p>
            )}
          </div>

          {/* Base URL */}
          <div className="space-y-2">
            <Label htmlFor="baseURL">Base URL</Label>
            <div className="space-y-2">
              <Select
                value={showCustomBaseURL ? "custom" : watchedValues.baseURL}
                onValueChange={(value) => {
                  if (value === "custom") {
                    setShowCustomBaseURL(true);
                  } else {
                    setShowCustomBaseURL(false);
                    setValue("baseURL", value);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择常用 Base URL">
                    {showCustomBaseURL
                      ? `自定义: ${watchedValues.baseURL || "请输入URL"}`
                      : watchedValues.baseURL || "选择常用 Base URL"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {commonBaseURLs.map((url) => (
                    <SelectItem key={url} value={url}>
                      {url}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">自定义 URL</SelectItem>
                </SelectContent>
              </Select>
              {/* 只在选择自定义时显示输入框 */}
              {showCustomBaseURL && (
                <Input
                  id="baseURL"
                  placeholder="输入自定义 Base URL"
                  {...register("baseURL")}
                  className="w-full"
                />
              )}
            </div>
            {errors.baseURL && (
              <p className="text-sm text-red-500">{errors.baseURL.message}</p>
            )}
          </div>

          {/* Model */}
          <div className="space-y-2">
            <Label htmlFor="model">模型</Label>
            <div className="space-y-2">
              <Select
                value={showCustomModel ? "custom" : watchedValues.model}
                onValueChange={(value) => {
                  if (value === "custom") {
                    setShowCustomModel(true);
                  } else {
                    setShowCustomModel(false);
                    setValue("model", value);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择常用模型">
                    {showCustomModel
                      ? `自定义: ${watchedValues.model || "请输入模型名称"}`
                      : watchedValues.model || "选择常用模型"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">自定义模型</SelectItem>
                </SelectContent>
              </Select>
              {/* 只在选择自定义时显示输入框 */}
              {showCustomModel && (
                <Input
                  id="model"
                  placeholder="输入自定义模型名称"
                  {...register("model")}
                  className="w-full"
                />
              )}
            </div>
            {errors.model && (
              <p className="text-sm text-red-500">{errors.model.message}</p>
            )}
          </div>

          {/* 测试结果 */}
          {testResult && (
            <Alert
              className={
                testResult.success
                  ? "border-green-200 bg-green-50"
                  : "border-red-200 bg-red-50"
              }
            >
              <div className="flex items-center gap-2">
                {testResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription
                  className={
                    testResult.success ? "text-green-800" : "text-red-800"
                  }
                >
                  {testResult.message}
                  {testResult.latency && ` (响应时间: ${testResult.latency}ms)`}
                </AlertDescription>
              </div>
            </Alert>
          )}

          {/* 按钮组 */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              保存配置
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={handleTest}
              disabled={isTesting || !watchedValues.apiKey}
            >
              {isTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              测试连接
            </Button>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button type="button" variant="destructive">
                  重置配置
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认重置配置</AlertDialogTitle>
                  <AlertDialogDescription>
                    此操作将清除所有已保存的配置信息，包括 API Key、Base URL
                    和模型设置。 配置将恢复为默认值，此操作不可撤销。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleReset}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    确认重置
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
